# Data Model Generator Utility

This utility generates a data model in draw.io format by combining entities from the entity dictionary analyzer and relationships from the relationship analyzer.

## Purpose

The Data Model Generator creates a visual representation of the SDS Entity Inventory data model by:

- Loading entity definitions from the entity dictionary analyzer
- Loading relationship mappings from the relationship analyzer  
- Positioning entities in a circular layout for optimal visualization
- Generating connections between related entities
- Outputting a draw.io compatible XML file

## Directory Structure

```
data_model_generator/
├── .outputs/                     # Hidden directory for output files
├── data_model_generator.py       # Main script
└── README.md                     # This documentation file
```

## Prerequisites

Before running this utility, you must first run:

1. **Entity Dictionary Analyzer**: To generate entity definitions
   ```bash
   cd configs/scripts/utilities/entity_dictionary_analyzer
   python3 entity_dictionary_analyzer.py
   ```

2. **Relationship Analyzer**: To generate relationship mappings
   ```bash
   cd configs/scripts/utilities/relationship_analyzer
   python3 relationship_analyzer.py
   ```

## Input Dependencies

### Entity Data
- **Source**: `configs/scripts/utilities/entity_dictionary_analyzer/.outputs/entity_descriptions.csv`
- **Format**: CSV with `entity_name` and `description` columns
- **Content**: Entity definitions and descriptions

### Relationship Data
- **Source**: `configs/scripts/utilities/relationship_analyzer/.outputs/relationship_analysis_*.csv`
- **Format**: CSV with `relationship_name`, `source_entity`, `target_entity` columns
- **Content**: Entity relationship mappings (uses most recent file)

## Output

### Draw.io File
- **Location**: `.outputs/data_model_YYYYMMDD_HHMMSS.drawio`
- **Format**: XML file compatible with draw.io
- **Content**: Visual data model with entities and relationships

### Console Output
- Entity and relationship loading status
- Summary statistics including:
  - Total entities and relationships
  - Most connected entities
  - Complete entity list

## Usage

### Basic Usage

Navigate to the utility directory and run the script:

```bash
cd configs/scripts/utilities/data_model_generator
python3 data_model_generator.py
```

### Example Output

```
Starting Data Model Generator...
Loaded 17 entities
Loaded 88 relationships
Draw.io file saved to: .outputs/data_model_20250127_143022.drawio

==================================================
DATA MODEL GENERATION SUMMARY
==================================================
Total entities: 17
Total relationships: 88

Top 10 Most Connected Entities:
  Network                   : 34 connections
  Storage                   : 22 connections
  Finding                   : 18 connections
  Cloud Account             : 17 connections
  Host                      : 16 connections
  ...

Entities included in the model:
  - Account
  - Application
  - Assessment
  - Cloud Account
  - Cluster
  ...
```

## Features

### Intelligent Layout
- **Circular Positioning**: Entities arranged in a circle for balanced visualization
- **Automatic Spacing**: Optimal spacing based on number of entities
- **Color Coding**: Each entity gets a unique color from a predefined palette

### Relationship Mapping
- **Bidirectional Connections**: Shows relationships between entities
- **Duplicate Filtering**: Prevents duplicate connections
- **Self-Reference Handling**: Excludes self-referencing relationships

### Draw.io Compatibility
- **Standard Format**: Generates XML compatible with draw.io
- **Editable Output**: Can be opened and modified in draw.io
- **Professional Styling**: Uses consistent colors and formatting

### Error Handling
- **Dependency Checking**: Validates that prerequisite files exist
- **Data Validation**: Ensures entities exist before creating relationships
- **Graceful Degradation**: Continues processing even if some data is missing

## Customization

### Layout Configuration
You can modify the layout by editing these variables in the script:

```python
self.canvas_width = 1200      # Canvas width
self.canvas_height = 800      # Canvas height
self.radius = 300             # Circle radius for entity placement
self.entity_width = 120       # Entity box width
self.entity_height = 60       # Entity box height
```

### Color Palette
The entity colors can be customized by modifying the `self.colors` list in the script.

## Requirements

- Python 3.6+
- Access to entity dictionary analyzer outputs
- Access to relationship analyzer outputs
- Write permissions for the .outputs directory

## Integration with Draw.io

### Opening the Generated File
1. Go to [draw.io](https://app.diagrams.net/) or use the desktop application
2. Click "Open Existing Diagram"
3. Select the generated `.drawio` file
4. The data model will load with all entities and relationships

### Editing the Model
- **Move Entities**: Drag entities to reposition them
- **Add Labels**: Double-click relationships to add labels
- **Change Colors**: Use the format panel to modify entity colors
- **Add Details**: Include additional information in entity descriptions

## Troubleshooting

### Common Issues

1. **"Entity file not found" error**
   - Run the entity_dictionary_analyzer first
   - Verify the analyzer completed successfully

2. **"No relationship analysis files found" error**
   - Run the relationship_analyzer first
   - Check that CSV files were generated in the .outputs directory

3. **Empty or incomplete model**
   - Verify both prerequisite utilities ran successfully
   - Check that entity names match between the two data sources

### Validation Steps

1. **Check Entity Data**:
   ```bash
   ls -la configs/scripts/utilities/entity_dictionary_analyzer/.outputs/
   ```

2. **Check Relationship Data**:
   ```bash
   ls -la configs/scripts/utilities/relationship_analyzer/.outputs/
   ```

3. **Verify File Contents**:
   ```bash
   head -5 configs/scripts/utilities/entity_dictionary_analyzer/.outputs/entity_descriptions.csv
   ```

## Version History

- **v1.0** (2025-01-27): Initial implementation
  - Circular entity layout
  - Draw.io XML generation
  - Relationship mapping
  - Color-coded entities
  - Statistical reporting
