#!/usr/bin/env python3
"""
Data Model Generator Utility

This utility generates a data model in draw.io format by combining entities from the 
entity dictionary analyzer and relationships from the relationship analyzer.

Author: SDS Entity Inventory Team
Created: 2025-01-27
"""

import csv
import math
import xml.etree.ElementTree as ET
from pathlib import Path
from datetime import datetime
from collections import defaultdict
import colorsys


class DataModelGenerator:
    """
    A utility class for generating data models in draw.io format.
    
    This class reads entity and relationship data from CSV files and generates
    a draw.io XML file with entities positioned in a circular layout.
    """
    
    def __init__(self):
        """Initialize the DataModelGenerator."""
        self.script_dir = Path(__file__).parent
        self.project_root = self.script_dir.parent.parent.parent.parent
        self.output_dir = self.script_dir / '.outputs'
        
        # Ensure output directory exists
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Data storage
        self.entities = {}
        self.relationships = []
        
        # Layout configuration
        self.canvas_width = 1200
        self.canvas_height = 800
        self.center_x = self.canvas_width // 2
        self.center_y = self.canvas_height // 2
        self.radius = 300
        self.entity_width = 120
        self.entity_height = 60
        
        # Color palette for entities
        self.colors = [
            "#a8e6cf",  # Light green
            "#ffd3b5",  # Light orange
            "#ff8b94",  # Light red
            "#c3bef0",  # Light purple
            "#87ceeb",  # Light blue
            "#f0e68c",  # Light yellow
            "#dda0dd",  # Plum
            "#98fb98",  # Pale green
            "#f5deb3",  # Wheat
            "#ffc0cb",  # Pink
            "#b0e0e6",  # Powder blue
            "#ffe4e1",  # Misty rose
            "#e0ffff",  # Light cyan
            "#f5f5dc",  # Beige
            "#ffe4b5",  # Moccasin
            "#d3d3d3",  # Light gray
            "#ffb6c1"   # Light pink
        ]
    
    def load_entities(self):
        """Load entities from the entity dictionary analyzer output."""
        entity_file = (
            self.project_root / 
            'configs/scripts/utilities/entity_dictionary_analyzer/.outputs/entity_descriptions.csv'
        )
        
        if not entity_file.exists():
            print(f"Warning: Entity file not found at {entity_file}")
            print("Please run the entity_dictionary_analyzer first.")
            return False
        
        with open(entity_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                entity_name = row['entity_name'].title().replace('_', ' ')
                self.entities[entity_name] = {
                    'description': row['description'],
                    'original_name': row['entity_name']
                }
        
        print(f"Loaded {len(self.entities)} entities")
        return True
    
    def load_relationships(self):
        """Load relationships from the relationship analyzer output."""
        rel_dir = self.project_root / 'configs/scripts/utilities/relationship_analyzer/.outputs'
        
        if not rel_dir.exists():
            print(f"Warning: Relationship directory not found at {rel_dir}")
            print("Please run the relationship_analyzer first.")
            return False
        
        # Find the most recent relationship file
        rel_files = list(rel_dir.glob('relationship_analysis_*.csv'))
        if not rel_files:
            print("Warning: No relationship analysis files found.")
            return False
        
        latest_file = max(rel_files, key=lambda f: f.stat().st_mtime)
        
        with open(latest_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                source = row['source_entity'].title().replace('_', ' ')
                target = row['target_entity'].title().replace('_', ' ')
                
                # Only include relationships where both entities exist
                if source in self.entities and target in self.entities:
                    self.relationships.append({
                        'source': source,
                        'target': target,
                        'name': row['relationship_name']
                    })
        
        print(f"Loaded {len(self.relationships)} relationships")
        return True
    
    def calculate_entity_positions(self):
        """Calculate positions for entities in a circular layout."""
        entity_list = list(self.entities.keys())
        num_entities = len(entity_list)
        
        positions = {}
        
        if num_entities == 0:
            return positions
        
        # Calculate angle between entities
        angle_step = 2 * math.pi / num_entities
        
        for i, entity in enumerate(entity_list):
            angle = i * angle_step
            x = self.center_x + self.radius * math.cos(angle) - self.entity_width // 2
            y = self.center_y + self.radius * math.sin(angle) - self.entity_height // 2
            
            positions[entity] = {
                'x': int(x),
                'y': int(y),
                'color': self.colors[i % len(self.colors)]
            }
        
        return positions
    
    def generate_drawio_xml(self, positions):
        """Generate the draw.io XML content."""
        # Create root element
        root = ET.Element('mxGraphModel')
        root.set('dx', str(self.canvas_width))
        root.set('dy', str(self.canvas_height))
        root.set('grid', '1')
        root.set('gridSize', '10')
        root.set('guides', '1')
        root.set('tooltips', '1')
        root.set('connect', '1')
        root.set('arrows', '1')
        root.set('fold', '1')
        root.set('page', '1')
        root.set('pageScale', '1')
        root.set('pageWidth', '850')
        root.set('pageHeight', '1100')
        root.set('math', '0')
        root.set('shadow', '0')
        
        # Create root cells
        graph_root = ET.SubElement(root, 'root')
        cell0 = ET.SubElement(graph_root, 'mxCell')
        cell0.set('id', '0')
        
        cell1 = ET.SubElement(graph_root, 'mxCell')
        cell1.set('id', '1')
        cell1.set('parent', '0')
        
        # Add entities
        entity_id_map = {}
        for entity, pos in positions.items():
            entity_id = entity.lower().replace(' ', '_')
            entity_id_map[entity] = entity_id
            
            user_object = ET.SubElement(graph_root, 'UserObject')
            user_object.set('label', entity)
            user_object.set('fontcolor', '#333333')
            user_object.set('placeholders', '1')
            user_object.set('id', entity_id)
            
            mx_cell = ET.SubElement(user_object, 'mxCell')
            style = (f"rounded=1;whiteSpace=wrap;html=1;fillColor={pos['color']};"
                    f"strokeColor=none;fontSize=14;")
            mx_cell.set('style', style)
            mx_cell.set('vertex', '1')
            mx_cell.set('parent', '1')
            
            geometry = ET.SubElement(mx_cell, 'mxGeometry')
            geometry.set('x', str(pos['x']))
            geometry.set('y', str(pos['y']))
            geometry.set('width', str(self.entity_width))
            geometry.set('height', str(self.entity_height))
            geometry.set('as', 'geometry')
        
        # Add relationships
        edge_id = 1
        for rel in self.relationships:
            source_id = entity_id_map.get(rel['source'])
            target_id = entity_id_map.get(rel['target'])

            if source_id and target_id and source_id != target_id:
                edge = ET.SubElement(graph_root, 'mxCell')
                edge.set('id', f'edge{edge_id}')
                edge.set('value', rel['name'])  # Set the relationship name as the edge label
                edge.set('style', 'endArrow=none;html=1;rounded=0;strokeWidth=2;strokeColor=#666666;')
                edge.set('edge', '1')
                edge.set('parent', '1')
                edge.set('source', source_id)
                edge.set('target', target_id)

                geometry = ET.SubElement(edge, 'mxGeometry')
                geometry.set('relative', '1')
                geometry.set('as', 'geometry')

                edge_id += 1
        
        return root
    
    def save_drawio_file(self, xml_root):
        """Save the draw.io XML to a file."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = self.output_dir / f'data_model_{timestamp}.drawio'
        
        # Create XML tree and write to file
        tree = ET.ElementTree(xml_root)
        ET.indent(tree, space="  ", level=0)
        
        with open(output_file, 'wb') as f:
            f.write(b'<?xml version="1.0" encoding="UTF-8"?>\n')
            tree.write(f, encoding='utf-8', xml_declaration=False)
        
        print(f"Draw.io file saved to: {output_file}")
        return str(output_file)
    
    def generate_statistics(self):
        """Generate and display statistics about the data model."""
        stats = {
            'total_entities': len(self.entities),
            'total_relationships': len(self.relationships),
            'entity_connections': defaultdict(int)
        }
        
        # Count connections per entity
        for rel in self.relationships:
            stats['entity_connections'][rel['source']] += 1
            stats['entity_connections'][rel['target']] += 1
        
        return stats
    
    def print_summary(self, stats):
        """Print a summary of the generated data model."""
        print(f"\n{'='*50}")
        print("DATA MODEL GENERATION SUMMARY")
        print(f"{'='*50}")
        
        print(f"Total entities: {stats['total_entities']}")
        print(f"Total relationships: {stats['total_relationships']}")
        
        print(f"\nTop 10 Most Connected Entities:")
        sorted_connections = sorted(
            stats['entity_connections'].items(), 
            key=lambda x: x[1], 
            reverse=True
        )
        
        for entity, count in sorted_connections[:10]:
            print(f"  {entity:25} : {count:2d} connections")
        
        print(f"\nEntities included in the model:")
        for entity in sorted(self.entities.keys()):
            print(f"  - {entity}")
    
    def run(self):
        """Main execution method that generates the complete data model."""
        print("Starting Data Model Generator...")
        print(f"Output directory: {self.output_dir}")
        
        # Load data
        if not self.load_entities():
            return None
        
        if not self.load_relationships():
            return None
        
        # Generate layout
        positions = self.calculate_entity_positions()
        
        # Generate draw.io XML
        xml_root = self.generate_drawio_xml(positions)
        
        # Save file
        output_file = self.save_drawio_file(xml_root)
        
        # Generate and display statistics
        stats = self.generate_statistics()
        self.print_summary(stats)
        
        return output_file


def main():
    """Main function to execute the data model generation."""
    try:
        generator = DataModelGenerator()
        output_file = generator.run()
        
        if output_file:
            print(f"\n{'='*50}")
            print("Data model generation completed successfully!")
            print(f"Draw.io file saved to: {output_file}")
            print("You can open this file in draw.io to view and edit the model.")
            print(f"{'='*50}")
        else:
            print("Data model generation failed. Please check the error messages above.")
        
    except KeyboardInterrupt:
        print("\nData model generation interrupted by user.")
    except Exception as e:
        print(f"\nError during generation: {e}")


if __name__ == "__main__":
    main()
